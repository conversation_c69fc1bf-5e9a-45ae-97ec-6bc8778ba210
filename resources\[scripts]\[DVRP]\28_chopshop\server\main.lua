local ox = exports.ox_lib

-- Reward player with items
RegisterNetEvent('chopshop:reward', function(type)
    local src = source
    local player = ox.getPlayer(src)
    if not player then return end
    
    local rewards = Config.Rewards[type]
    if not rewards then
        print(('^1[ERROR] Invalid reward type: %s^0'):format(type))
        return
    end
    
    for _, item in ipairs(rewards) do
        -- Check chance (percentage 0-100)
        if math.random(1, 100) <= item.chance then
            -- Calculate actual amount (random between min and max)
            local amount = math.random(item.min, item.max)
            
            if exports.ox_inventory:CanCarryItem(src, item.name, amount) then
                exports.ox_inventory:AddItem(src, item.name, amount)
            else
                ox.notify(src, {
                    title = 'Inventory Full',
                    description = ('Not enough space for %s %s'):format(amount, item.name),
                    type = 'error'
                })
            end
        end
    end
end)

-- Debug command
if Config.Debug then
    RegisterCommand('testreward', function(source)
        TriggerEvent('chopshop:reward', 'full', source)
    end, true)
end