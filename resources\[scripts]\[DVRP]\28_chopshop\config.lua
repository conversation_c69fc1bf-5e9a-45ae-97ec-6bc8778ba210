Config = {}

-- Notification settings
Config.Notify = 'okok' -- ox/qb/okok/custom
Config.NotifyText = {
    target = 'Press [E] to dismantle %s',
    start = 'Dismantling in progress...',
    parts = {
        door = 'You dismantled a door',
        hood = 'You dismantled a hood',
        wheel = 'You dismantled a wheel',
        full = 'You completely dismantled the vehicle'
    }
}

-- Key settings
Config.Keys = {
    dismantle = 38, -- E key
    lockpick = 47   -- G key (wasabi_carlock integration)
}

-- Item rewards with min amount and percentage chance
Config.Rewards = {
    door = {
        { name = 'scrap_metal', min = 1, max = 3, chance = 80 },
        { name = 'rubber', min = 1, max = 2, chance = 70 }
    },
    hood = {
        { name = 'scrap_metal', min = 2, max = 4, chance = 90 },
        { name = 'engine_part', min = 1, max = 2, chance = 60 }
    },
    wheel = {
        { name = 'rubber', min = 1, max = 3, chance = 75 },
        { name = 'scrap_metal', min = 1, max = 2, chance = 65 }
    },
    full = {
        { name = 'scrap_metal', min = 5, max = 12, chance = 100 },
        { name = 'engine_part', min = 2, max = 5, chance = 80 },
        { name = 'electronics', min = 1, max = 3, chance = 70 }
    }
}

-- Fuel settings
Config.SpawnFuel = 100 -- Initial fuel level for spawned vehicles (0-100)

-- Target models (vehicles that can be dismantled)
Config.TargetModels = {
    `blista`,
    `brioso`,
    `dilettante`,
    `issi2`,
    `panto`,
    `prairie`,
    `rhapsody`,
    `weevil`
}

-- Dismantle locations (vector3)
Config.DismantleLocations = {
    vector3(-422.08, -1681.69, 19.03) -- Example location, replace with actual
}

-- Spawn locations (vector4 - x, y, z, heading)
Config.SpawnLocations = {
    vector4(44.81, -1724.5, 30.3, 227.54) -- Example location, replace with actual 
}

-- NPC locations for taking orders (vector4 - x, y, z, heading)
Config.NPCLocations = {
    vector4(-440.49, -1693.76, 19.2, 161.27) -- Example location, replace with actual 
}

-- Search circle settings
Config.SearchCircle = {
    radius = 5.0, -- Search circle radius in meters
    points = 8,   -- Number of search points in circle
    timePerPoint = 5000, -- Time to search each point in ms
    marker = {
        type = 1,
        size = vector3(0.5, 0.5, 0.5),
        color = vector3(0, 255, 0)
    }
}

-- Debug mode
Config.Debug = false