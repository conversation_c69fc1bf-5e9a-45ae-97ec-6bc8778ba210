local lib, fuel

local dismantling = false
local currentVehicle = nil

-- Get localized text
function GetText(label)
    if lib then
        return lib.getLocale(label) or Config.NotifyText[label] or label
    else
        return Config.NotifyText[label] or label
    end
end

-- Wait for ox_lib to load before registering context
CreateThread(function()
    local startTime = GetGameTimer()
    local timeout = 30000 -- 30 seconds timeout

    -- Wait for ox_lib to be started and functions available
    while GetResourceState('ox_lib') ~= 'started' or not exports.ox_lib or
          not exports.ox_lib.registerMenu do
        Wait(100)
        if GetGameTimer() - startTime > timeout then
            print("^1[ERROR] Failed to load ox_lib after 30 seconds. Check if ox_lib resource is started.^7")
            return
        end
    end

    lib = exports.ox_lib
    fuel = exports.ox_fuel

    -- First register the menu type
    lib.registerMenu({
        id = 'chopshop_menu',
        title = 'Chop Shop',
        position = 'top-right',
        options = {
            {label = 'Accept Mission', description = 'Accept a vehicle dismantling mission'},
            {label = 'Decline Mission', description = 'Decline the mission'}
        }
    }, function(selected, scrollIndex, args)
        if selected == 1 then
            print("Mission started! Find the target vehicle")
        elseif selected == 2 then
            print("Mission declined")
        end
    end)

    print("Successfully registered chopshop menu")
end)

-- NPC creation and targeting
local npcs = {}

local function createNPC(location)
    local model = joaat('a_m_m_hillbilly_01') -- Use joaat for model hashes
    RequestModel(model)
    while not HasModelLoaded(model) do
        Wait(0)
    end
    local npc = CreatePed(0, model, location.x, location.y, location.z, location.w, false, false)
    FreezeEntityPosition(npc, true)
    SetEntityInvincible(npc, true)
    SetBlockingOfNonTemporaryEvents(npc, true)
    return npc
end

-- Create NPCs at defined locations
CreateThread(function()
    -- Wait for ox_target to load
    while not exports.ox_target do
        Wait(100)
    end

    for i, loc in ipairs(Config.NPCLocations) do
        local npc = createNPC(loc)
        npcs[i] = npc
        
        -- Add ox_target options to NPC
        exports.ox_target:addLocalEntity(npc, {
            {
                name = 'chopshop_talk',
                icon = 'fas fa-comment',
                label = GetText('talk_to_npc'),
                onSelect = function()
                    -- Show progress bar first
                    if lib.progressBar then
                        lib.progressBar({
                            duration = 3000,
                            label = GetText('talking_to_npc'),
                            useWhileDead = false,
                            canCancel = true,
                            disable = {
                                move = true,
                                combat = true,
                            },
                            anim = {
                                dict = 'missheistdockssetup1leadinout@idle_a',
                                clip = 'leadin_loop'
                            }
                        }, function(cancelled)
                            if not cancelled then
                                -- Show mission menu after progress
                                lib.showMenu('chopshop_menu')
                            end
                        end)
                    else
                        -- Fallback if progressBar isn't available
                        Wait(3000)
                        lib.showMenu('chopshop_menu')
                    end
                end,
                distance = 2.5
            }
        })
    end
end)

-- Clean up NPCs on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        for _, npc in ipairs(npcs) do
            exports.ox_target:removeLocalEntity(npc)
            DeleteEntity(npc)
        end
    end
end)

-- Generate search points around vehicle
local function generateSearchPoints(vehicle, radius, count)
    local coords = GetEntityCoords(vehicle)
    local points = {}
    for i = 1, count do
        local angle = (math.pi * 2) * (i / count)
        local x = coords.x + math.cos(angle) * radius
        local y = coords.y + math.sin(angle) * radius
        local z = coords.z
        points[i] = vector3(x, y, z)
    end
    return points
end

-- Start dismantling process with circle search
local function startDismantle(vehicle)
    if dismantling then return end
    currentVehicle = vehicle
    dismantling = true
    
    -- Lock vehicle using wasabi_carlock
    TriggerEvent('wasabi_carlock:lockVehicle', vehicle, true)
    
    -- Generate search points
    local points = generateSearchPoints(vehicle, Config.SearchCircle.radius, Config.SearchCircle.points)
    local completedPoints = 0
    
    -- Create targets for each search point
    for i, point in ipairs(points) do
        exports.ox_target:addSphereZone({
            coords = point,
            radius = 1.0,
            debug = Config.Debug,
            options = {
                {
                    name = 'chopshop_search_'..i,
                    icon = 'fas fa-search',
                    label = GetText('search_point') or 'Search',
                    onSelect = function()
                        -- Start progress bar for this point
                        lib.progressBar({
                            label = GetText('search_point') or 'Searching...',
                            duration = Config.SearchCircle.timePerPoint,
                            canCancel = true,
                            disable = {
                                move = true,
                                combat = true,
                                car = true
                            },
                            anim = {
                                dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@',
                                clip = 'machinic_loop_mechandplayer'
                            }
                        }, function(success)
                            if success then
                                completedPoints = completedPoints + 1
                                
                                -- Check if all points are completed
                                if completedPoints >= #points then
                                    -- Notify and reward player
                                    TriggerServerEvent('chopshop:reward', 'full')
                                    lib.notify({description = GetText('parts.full')})
                                    
                                    -- Clean up
                                    dismantling = false
                                    currentVehicle = nil
                                    TriggerEvent('wasabi_carlock:lockVehicle', vehicle, false)
                                end
                            end
                        end)
                    end,
                    distance = 2.0
                }
            }
        })
    end
end

-- Vehicle targeting
CreateThread(function()
    for _, model in ipairs(Config.TargetModels) do
        exports.ox_target:addModel(model, {
            {
                name = 'chopshop_dismantle',
                icon = 'fas fa-screwdriver',
                label = GetText('target'):format(GetLabelText(GetDisplayNameFromVehicleModel(model))),
                canInteract = function(entity)
                    return not dismantling and not Entity(entity).state.locked
                end,
                onSelect = function(data)
                    startDismantle(data.entity)
                end,
                distance = 2.5
            }
        })
    end
end)

-- Key controls
RegisterCommand('chopshop_lockpick', function()
    if not currentVehicle then return end
    -- Lockpick integration would go here
    lib.notify({description = 'Lockpicking vehicle...'})
end, false)

RegisterKeyMapping('chopshop_lockpick', 'Lockpick Vehicle', 'keyboard', Config.Keys.lockpick)

-- Add search_point to notification texts
Config.NotifyText.search_point = 'Search area'